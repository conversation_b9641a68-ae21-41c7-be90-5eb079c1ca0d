import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../widgets/payments/payment_modal_web.dart';

/// Helper class for testing payment functionality
class PaymentTestHelper {
  /// Show payment modal for testing
  static Future<void> showTestPaymentModal(BuildContext context, double amount) async {
    if (!kIsWeb) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Payment modal is only available on web'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PaymentModalWeb(
        amount: amount,
        onSuccess: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Test payment of \$${amount.toStringAsFixed(2)} completed!'),
              backgroundColor: Colors.green,
            ),
          );
        },
        onCancel: () {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment cancelled'),
              backgroundColor: Colors.orange,
            ),
          );
        },
      ),
    );
  }

  /// Test payment amounts for development
  static const List<double> testAmounts = [1.00, 5.00, 10.00, 25.00, 50.00];

  /// Show test payment options
  static void showTestPaymentOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Test Payment Modal'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Select an amount to test the payment modal:'),
            const SizedBox(height: 16),
            ...testAmounts.map((amount) => 
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      showTestPaymentModal(context, amount);
                    },
                    child: Text('\$${amount.toStringAsFixed(2)}'),
                  ),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
