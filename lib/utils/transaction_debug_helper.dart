import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/wallet_controller.dart';
import '../models/transaction_model.dart';

/// Helper class for debugging transaction issues
class TransactionDebugHelper {
  static final WalletController _walletController = Get.find<WalletController>();

  /// Show debug information about current transactions
  static void showTransactionDebugInfo(BuildContext context) {
    if (!kDebugMode) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Debug Info'),
        content: SizedBox(
          width: 400,
          height: 500,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Wallet Balance: \$${_walletController.balance.toStringAsFixed(2)}'),
                Text('Wallet Status: ${_walletController.status}'),
                Text('Total Transactions: ${_walletController.transactions.length}'),
                const SizedBox(height: 16),
                const Text('Recent Transactions:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                ..._walletController.transactions.take(10).map((transaction) => 
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('ID: ${transaction.id}'),
                          Text('Type: ${transaction.type.name}'),
                          Text('Amount: \$${transaction.amount.toStringAsFixed(2)}'),
                          Text('Status: ${transaction.status.name}'),
                          Text('Description: ${transaction.description}'),
                          Text('Timestamp: ${transaction.timestamp}'),
                          if (transaction.postId != null) Text('Post ID: ${transaction.postId}'),
                          if (transaction.externalTransactionId != null) 
                            Text('External ID: ${transaction.externalTransactionId}'),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          if (kDebugMode)
            TextButton(
              onPressed: () {
                _printTransactionDebugInfo();
                Navigator.of(context).pop();
              },
              child: const Text('Print to Console'),
            ),
        ],
      ),
    );
  }

  /// Print detailed debug information to console
  static void _printTransactionDebugInfo() {
    if (!kDebugMode) return;

    print('=== TRANSACTION DEBUG INFO ===');
    print('Wallet Balance: \$${_walletController.balance.toStringAsFixed(2)}');
    print('Wallet Status: ${_walletController.status}');
    print('Total Transactions: ${_walletController.transactions.length}');
    print('Total Earnings: \$${_walletController.totalEarnings.toStringAsFixed(2)}');
    print('Total Spent: \$${_walletController.totalSpent.toStringAsFixed(2)}');
    print('Last Updated: ${_walletController.lastUpdated}');
    
    print('\n=== RECENT TRANSACTIONS ===');
    for (int i = 0; i < _walletController.transactions.length && i < 20; i++) {
      final transaction = _walletController.transactions[i];
      print('${i + 1}. ${transaction.type.name.toUpperCase()} - \$${transaction.amount.toStringAsFixed(2)}');
      print('   ID: ${transaction.id}');
      print('   Description: ${transaction.description}');
      print('   Status: ${transaction.status.name}');
      print('   Timestamp: ${transaction.timestamp}');
      if (transaction.postId != null) print('   Post ID: ${transaction.postId}');
      if (transaction.externalTransactionId != null) print('   External ID: ${transaction.externalTransactionId}');
      print('   ---');
    }
    print('=== END DEBUG INFO ===');
  }

  /// Create a test transaction for debugging
  static Future<void> createTestTransaction(BuildContext context) async {
    if (!kDebugMode) return;

    try {
      final success = await _walletController.deductFunds(
        amount: 0.01,
        description: 'Test transaction - ${DateTime.now().millisecondsSinceEpoch}',
        postId: 'test_post_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'Test transaction created' : 'Test transaction failed'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test transaction error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show transaction type breakdown
  static void showTransactionBreakdown(BuildContext context) {
    final transactions = _walletController.transactions;
    final creditCount = transactions.where((t) => t.isCredit).length;
    final debitCount = transactions.where((t) => t.isDebit).length;
    final pendingCount = transactions.where((t) => t.isPending).length;
    final completedCount = transactions.where((t) => t.isCompleted).length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Breakdown'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total Transactions: ${transactions.length}'),
            const SizedBox(height: 8),
            Text('Credits: $creditCount'),
            Text('Debits: $debitCount'),
            const SizedBox(height: 8),
            Text('Completed: $completedCount'),
            Text('Pending: $pendingCount'),
            const SizedBox(height: 8),
            Text('Balance: \$${_walletController.balance.toStringAsFixed(2)}'),
            Text('Total Earnings: \$${_walletController.totalEarnings.toStringAsFixed(2)}'),
            Text('Total Spent: \$${_walletController.totalSpent.toStringAsFixed(2)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
