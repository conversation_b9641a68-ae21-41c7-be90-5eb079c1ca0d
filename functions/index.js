const functions = require("firebase-functions");
const stripe = require("stripe")(functions.config().stripe.secret_key);
const cors = require("cors")({ origin: true });

exports.createPaymentIntent = functions.https.onRequest(async (req, res) => {
  // Handle CORS preflight requests
  return cors(req, res, async () => {
    try {
      // Set additional CORS headers
      res.set({
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-Requested-With, Accept, Origin",
        "Access-Control-Max-Age": "86400", // 24 hours
        "Content-Type": "application/json",
      });

      // Handle OPTIONS preflight request
      if (req.method === "OPTIONS") {
        return res.status(200).send();
      }

      // Only allow POST requests for creating payment intents
      if (req.method !== "POST") {
        return res.status(405).json({
          error: "Method not allowed. Only POST requests are accepted.",
          success: false,
        });
      }

      // Validate request body
      const { amount, currency = "usd", email } = req.body || {};

      if (!amount || !email) {
        return res.status(400).json({
          error: "Missing required fields: amount and email are required.",
          success: false,
        });
      }
      if (typeof amount !== "number") {
        //  Parse
        amount = parseFloat(amount);
        if (amount <= 0) {
          return res.status(400).json({
            error: "Amount must be a positive number.",
            success: false,
          });
        }
      }
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: "Invalid email format.",
          success: false,
        });
      }

      // Find or create customer
      let customer;
      try {
        const customers = await stripe.customers.list({ email, limit: 1 });
        customer = customers.data[0]
          ? customers.data[0]
          : await stripe.customers.create({ email });
      } catch (stripeError) {
        console.error("Error handling customer:", stripeError);
        return res.status(500).json({
          error: "Failed to create or retrieve customer.",
          success: false,
        });
      }

      // Create ephemeral key
      let ephemeralKey;
      try {
        ephemeralKey = await stripe.ephemeralKeys.create(
          { customer: customer.id },
          { apiVersion: "2020-08-27" }
        );
      } catch (stripeError) {
        console.error("Error creating ephemeral key:", stripeError);
        return res.status(500).json({
          error: "Failed to create ephemeral key.",
          success: false,
        });
      }

      // Create payment intent
      let paymentIntent;
      try {
        paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount), // Ensure amount is an integer
          currency,
          customer: customer.id,
          automatic_payment_methods: {
            enabled: true,
          },
        });
      } catch (stripeError) {
        console.error("Error creating payment intent:", stripeError);
        return res.status(500).json({
          error: "Failed to create payment intent.",
          success: false,
        });
      }

      // Return successful response
      return res.status(200).json({
        paymentIntent: paymentIntent.client_secret,
        ephemeralKey: ephemeralKey.secret,
        customer: customer.id,
        customerEmail: customer.email,
        success: true,
      });
    } catch (error) {
      console.error("Unexpected error creating payment intent:", error);
      return res.status(500).json({
        error: "An unexpected error occurred. Please try again.",
        success: false,
      });
    }
  });
});
